"""
Build script for creating a protected version of Fusion Launcher
Implements multiple layers of protection against decompilation
"""

import os
import sys
import shutil
import subprocess
import tempfile
from pathlib import Path

class ProtectedBuilder:
    """Handles the complete build process with protection"""

    def __init__(self):
        self.project_dir = Path.cwd()
        self.build_dir = self.project_dir / "build_protected"
        self.dist_dir = self.project_dir / "dist_protected"

        # Ensure build directories exist
        self.build_dir.mkdir(exist_ok=True)
        self.dist_dir.mkdir(exist_ok=True)

    def check_dependencies(self):
        """Check if all required dependencies are installed"""
        required_packages = [
            ('cython', 'Cython'),
            ('pyarmor', 'pyarmor'),
            ('pyinstaller', 'PyInstaller'),
            ('numpy', 'numpy')
        ]

        missing_packages = []

        for package_name, import_name in required_packages:
            try:
                __import__(import_name)
            except ImportError:
                missing_packages.append(package_name)

        if missing_packages:
            print(f"Missing required packages: {', '.join(missing_packages)}")
            print("Install them with: pip install " + " ".join(missing_packages))
            return False

        return True

    def compile_cython_modules(self):
        """Compile core modules with Cython for additional protection"""
        print("Compiling core modules with Cython...")

        try:
            # Run Cython compilation
            result = subprocess.run([
                sys.executable, "setup.py", "build_ext", "--inplace"
            ], capture_output=True, text=True, cwd=self.project_dir)

            if result.returncode != 0:
                print(f"Cython compilation failed: {result.stderr}")
                return False

            print("Cython compilation successful!")
            return True

        except Exception as e:
            print(f"Error during Cython compilation: {e}")
            return False

    def obfuscate_with_pyarmor(self):
        """Obfuscate the main launcher with PyArmor"""
        print("Obfuscating with PyArmor...")

        try:
            # Create PyArmor project
            armor_dir = self.build_dir / "armored"
            armor_dir.mkdir(exist_ok=True)

            # Initialize PyArmor project
            subprocess.run([
                "pyarmor", "gen", "--output", str(armor_dir),
                "--platform", "windows.x86_64",
                "--enable-jit",  # Enable JIT protection
                "--mix-str",     # String obfuscation
                "--assert-call", # Anti-debugging
                "--assert-import", # Import protection
                "FusionLauncher.py"
            ], check=True, cwd=self.project_dir)

            print("PyArmor obfuscation successful!")
            return True

        except subprocess.CalledProcessError as e:
            print(f"PyArmor obfuscation failed: {e}")
            return False
        except Exception as e:
            print(f"Error during PyArmor obfuscation: {e}")
            return False

    def build_with_simple_pyinstaller(self):
        """Build with simple PyInstaller command"""
        print("Building with simple PyInstaller...")

        try:
            # Simple PyInstaller command
            cmd = [
                sys.executable, "-m", "PyInstaller",
                "--onefile",
                "--noconsole",
                "--clean",
                "--distpath", str(self.dist_dir),
                "--workpath", str(self.build_dir / "pyinstaller"),
                "--add-data", "core_module.cp313-win_amd64.pyd;.",
                "--add-data", "protection.cp313-win_amd64.pyd;.",
                "--hidden-import", "core_module",
                "--hidden-import", "protection",
                "--hidden-import", "customtkinter",
                "--hidden-import", "PIL",
                "--hidden-import", "requests",
                "--hidden-import", "psutil",
                "--hidden-import", "pypresence",
                "--hidden-import", "numpy",
                "FusionLauncher.py"
            ]

            result = subprocess.run(cmd, capture_output=True, text=True, cwd=self.project_dir)

            if result.returncode != 0:
                print(f"PyInstaller build failed:")
                print(f"STDOUT: {result.stdout}")
                print(f"STDERR: {result.stderr}")
                return False

            print("Executable build successful!")
            return True

        except Exception as e:
            print(f"Error during PyInstaller build: {e}")
            return False

    def build_executable(self):
        """Build the final executable with PyInstaller"""
        return self.build_with_simple_pyinstaller()

    def apply_additional_protection(self):
        """Apply additional protection measures"""
        print("Applying additional protection measures...")

        try:
            # Find the built executable
            exe_path = self.dist_dir / "FusionLauncher.exe"

            if not exe_path.exists():
                print("Executable not found!")
                return False

            # Apply UPX compression if available
            try:
                subprocess.run(["upx", "--best", str(exe_path)],
                             capture_output=True, check=True)
                print("UPX compression applied!")
            except (subprocess.CalledProcessError, FileNotFoundError):
                print("UPX not available or failed - skipping compression")

            # Create checksum file
            import hashlib
            with open(exe_path, 'rb') as f:
                exe_hash = hashlib.sha256(f.read()).hexdigest()

            checksum_file = exe_path.parent / "checksum.txt"
            with open(checksum_file, 'w') as f:
                f.write(f"SHA256: {exe_hash}\n")
                f.write(f"File: {exe_path.name}\n")

            print("Additional protection measures applied!")
            return True

        except Exception as e:
            print(f"Error applying additional protection: {e}")
            return False

    def cleanup_build_files(self):
        """Clean up temporary build files"""
        print("Cleaning up build files...")

        try:
            # Remove temporary files
            temp_files = [
                "FusionLauncher_protected.spec",
                "core_module.c",
                "protection.c",
                "core_module.pyd",
                "protection.pyd"
            ]

            for temp_file in temp_files:
                file_path = self.project_dir / temp_file
                if file_path.exists():
                    file_path.unlink()

            # Remove build directory
            if self.build_dir.exists():
                shutil.rmtree(self.build_dir)

            print("Cleanup completed!")

        except Exception as e:
            print(f"Error during cleanup: {e}")

    def build(self, cleanup=True):
        """Execute the complete build process"""
        print("Starting protected build process...")
        print("=" * 50)

        # Check dependencies
        if not self.check_dependencies():
            return False

        # Step 1: Compile Cython modules
        if not self.compile_cython_modules():
            return False

        # Step 2: Obfuscate with PyArmor (optional - comment out if not needed)
        # if not self.obfuscate_with_pyarmor():
        #     return False

        # Step 3: Build executable
        if not self.build_executable():
            return False

        # Step 4: Apply additional protection
        if not self.apply_additional_protection():
            return False

        # Step 5: Cleanup (optional)
        if cleanup:
            self.cleanup_build_files()

        print("=" * 50)
        print("Protected build completed successfully!")
        print(f"Output directory: {self.dist_dir}")

        return True

def main():
    """Main build function"""
    builder = ProtectedBuilder()

    # Parse command line arguments
    cleanup = "--no-cleanup" not in sys.argv

    success = builder.build(cleanup=cleanup)

    if success:
        print("\n✅ Build completed successfully!")
        print(f"📁 Protected executable: {builder.dist_dir / 'FusionLauncher.exe'}")
    else:
        print("\n❌ Build failed!")
        sys.exit(1)

if __name__ == "__main__":
    main()
