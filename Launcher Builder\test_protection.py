"""
Test script to verify protection features are working
"""

def test_cython_modules():
    """Test if Cython modules can be imported"""
    try:
        import core_module
        print("✅ core_module imported successfully")
        
        # Test core functionality
        core = core_module.create_core_launcher()
        if core:
            print("✅ CoreLauncher created successfully")
            
            # Test protected data access
            config = core.get_runtime_config()
            if config and len(config) > 0:
                print("✅ Protected configuration loaded successfully")
                print(f"   - Version URL: {'***PROTECTED***' if config.get('version_url') else 'MISSING'}")
                print(f"   - EXE URL: {'***PROTECTED***' if config.get('exe_url') else 'MISSING'}")
                print(f"   - Discord Client ID: {'***PROTECTED***' if config.get('discord_client_id') else 'MISSING'}")
            else:
                print("❌ Failed to load protected configuration")
        else:
            print("❌ Failed to create Core<PERSON>auncher")
            
    except ImportError as e:
        print(f"❌ Failed to import core_module: {e}")
    except Exception as e:
        print(f"❌ Error testing core_module: {e}")

def test_protection_module():
    """Test if protection module is working"""
    try:
        import protection
        print("✅ protection module imported successfully")
        
        # Test protection manager
        pm = protection.get_protection_manager()
        if pm:
            print("✅ ProtectionManager created successfully")
            
            # Test environment verification
            is_safe = protection.verify_execution_environment()
            print(f"✅ Environment verification: {'SAFE' if is_safe else 'UNSAFE'}")
            
            # Get protection status
            status = pm.get_protection_status()
            print(f"✅ Protection status:")
            print(f"   - Active: {status.get('active', False)}")
            print(f"   - Debug detected: {status.get('debug_detected', False)}")
            print(f"   - Safe environment: {status.get('safe_environment', False)}")
            print(f"   - Uptime: {status.get('uptime', 0):.2f}s")
        else:
            print("❌ Failed to create ProtectionManager")
            
    except ImportError as e:
        print(f"❌ Failed to import protection: {e}")
    except Exception as e:
        print(f"❌ Error testing protection: {e}")

def test_string_obfuscation():
    """Test string obfuscation functionality"""
    try:
        import core_module
        core = core_module.create_core_launcher()
        
        if core:
            # Test that URLs are properly obfuscated in memory
            test_string = "test_string_123"
            obfuscated = core._obfuscate_string(test_string)
            deobfuscated = core._deobfuscate_string(obfuscated)
            
            if deobfuscated == test_string:
                print("✅ String obfuscation/deobfuscation working correctly")
                print(f"   - Original: {test_string}")
                print(f"   - Obfuscated: {obfuscated[:20]}...")
                print(f"   - Deobfuscated: {deobfuscated}")
            else:
                print("❌ String obfuscation failed")
        else:
            print("❌ Cannot test string obfuscation - core module not available")
            
    except Exception as e:
        print(f"❌ Error testing string obfuscation: {e}")

def test_fallback_mode():
    """Test fallback mode when protection is not available"""
    print("\n🔄 Testing fallback mode...")
    
    # This simulates what happens when protection modules are not available
    try:
        # Test basic launcher functionality without protection
        import FusionLauncher
        print("✅ FusionLauncher can be imported")
        
        # The launcher should work even without protection modules
        print("✅ Fallback mode should be functional")
        
    except Exception as e:
        print(f"❌ Error in fallback mode: {e}")

def main():
    """Run all protection tests"""
    print("🔒 Fusion Launcher Protection System Test")
    print("=" * 50)
    
    print("\n📦 Testing Cython Modules:")
    test_cython_modules()
    
    print("\n🛡️ Testing Protection Features:")
    test_protection_module()
    
    print("\n🔐 Testing String Obfuscation:")
    test_string_obfuscation()
    
    test_fallback_mode()
    
    print("\n" + "=" * 50)
    print("🏁 Protection system test completed!")
    print("\nIf all tests show ✅, your protection system is working correctly.")
    print("If any tests show ❌, check the error messages above.")

if __name__ == "__main__":
    main()
