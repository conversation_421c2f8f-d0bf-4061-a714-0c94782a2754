# Requirements for building protected version of Fusion Client

# Core client dependencies
customtkinter>=5.2.0
pymem>=1.10.0
psutil>=5.9.0
keyboard>=0.13.5
pygame>=2.5.0
Pillow>=10.0.0
requests>=2.31.0
pywin32>=306

# Protection and build dependencies
cython>=3.0.0
pyarmor>=8.4.0
pyinstaller>=6.0.0
numpy>=1.24.0

# Optional compression
upx-ucl>=4.0.0  # Note: UPX needs to be installed separately

# Development tools (for testing)
pytest>=7.4.0
pytest-cov>=4.1.0
