"""
Setup script for compiling Fusion Client modules to Cython extensions
This provides additional protection against decompilation
"""

from setuptools import setup, Extension
from Cython.Build import cythonize
import numpy

# Define extensions to compile
extensions = [
    Extension(
        "client_core_module",
        ["client_core_module.py"],
        include_dirs=[numpy.get_include()],
    ),
    Extension(
        "client_protection",
        ["client_protection.py"],
        include_dirs=[numpy.get_include()],
    )
]

# Cython compiler directives for optimization and obfuscation
compiler_directives = {
    'language_level': 3,
    'boundscheck': False,
    'wraparound': False,
    'initializedcheck': False,
    'cdivision': True,
    'embedsignature': False,  # Don't embed function signatures
    'emit_code_comments': False,  # Don't emit code comments
    'profile': False,  # Disable profiling
}

setup(
    name="FusionClientCore",
    ext_modules=cythonize(
        extensions,
        compiler_directives=compiler_directives,
        build_dir="build",
        language_level=3
    ),
    zip_safe=False,
)
