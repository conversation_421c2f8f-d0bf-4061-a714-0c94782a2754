"""
Protection module for Fusion Launcher
Implements anti-debugging and runtime protection measures
"""

import os
import sys
import time
import psutil
import threading
import hashlib
import random
import string
from pathlib import Path

class ProtectionManager:
    """Manages runtime protection and anti-debugging measures"""
    
    def __init__(self):
        self.protection_active = True
        self.debug_detected = False
        self.monitoring_thread = None
        self.start_time = time.time()
        
        # Obfuscated strings
        self._strings = self._init_strings()
        
        # Start protection monitoring
        self.start_monitoring()
    
    def _init_strings(self):
        """Initialize obfuscated strings"""
        # Simple XOR obfuscation for sensitive strings
        key = 0x42
        strings = {}
        
        # Obfuscated debugger process names
        debugger_names = [
            self._xor_string("x64dbg.exe", key),
            self._xor_string("ollydbg.exe", key),
            self._xor_string("ida.exe", key),
            self._xor_string("ida64.exe", key),
            self._xor_string("windbg.exe", key),
            self._xor_string("cheatengine.exe", key),
            self._xor_string("processhacker.exe", key),
            self._xor_string("procmon.exe", key),
            self._xor_string("wireshark.exe", key)
        ]
        
        strings['debuggers'] = [self._xor_string(name, key) for name in debugger_names]
        return strings
    
    def _xor_string(self, text, key):
        """Simple XOR string obfuscation"""
        return ''.join(chr(ord(c) ^ key) for c in text)
    
    def _decode_string(self, encoded, key=0x42):
        """Decode XOR obfuscated string"""
        return self._xor_string(encoded, key)
    
    def check_debugger_processes(self):
        """Check for known debugger processes"""
        try:
            for proc in psutil.process_iter(['name']):
                proc_name = proc.info['name'].lower()
                
                # Check against obfuscated debugger names
                for encoded_name in self._strings['debuggers']:
                    debugger_name = self._decode_string(encoded_name).lower()
                    if debugger_name in proc_name:
                        self.debug_detected = True
                        return True
                        
                # Additional checks for common analysis tools
                suspicious_names = [
                    'fiddler', 'burp', 'wireshark', 'tcpview', 'regmon',
                    'filemon', 'procexp', 'autoruns', 'pestudio', 'pe-bear'
                ]
                
                for sus_name in suspicious_names:
                    if sus_name in proc_name:
                        self.debug_detected = True
                        return True
                        
        except Exception:
            pass
        
        return False
    
    def check_vm_environment(self):
        """Check if running in a virtual machine"""
        try:
            # Check for VM-specific processes
            vm_processes = [
                'vmtoolsd.exe', 'vmwaretray.exe', 'vmwareuser.exe',
                'vboxservice.exe', 'vboxtray.exe', 'xenservice.exe',
                'qemu-ga.exe', 'prl_tools.exe'
            ]
            
            for proc in psutil.process_iter(['name']):
                if proc.info['name'].lower() in vm_processes:
                    return True
                    
            # Check for VM-specific registry keys (Windows)
            if os.name == 'nt':
                import winreg
                vm_keys = [
                    r"SOFTWARE\VMware, Inc.\VMware Tools",
                    r"SOFTWARE\Oracle\VirtualBox Guest Additions",
                    r"SYSTEM\ControlSet001\Services\VBoxGuest"
                ]
                
                for key_path in vm_keys:
                    try:
                        winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, key_path)
                        return True
                    except FileNotFoundError:
                        continue
                        
        except Exception:
            pass
            
        return False
    
    def check_timing_attacks(self):
        """Check for timing-based analysis"""
        # Measure execution time for simple operations
        start = time.perf_counter()
        
        # Perform some dummy operations
        dummy = 0
        for i in range(1000):
            dummy += i * 2
            
        end = time.perf_counter()
        execution_time = end - start
        
        # If execution is too slow, might be under analysis
        if execution_time > 0.1:  # 100ms threshold
            return True
            
        return False
    
    def check_file_integrity(self):
        """Check if the current file has been modified"""
        try:
            current_file = Path(__file__)
            if current_file.exists():
                # Calculate file hash
                with open(current_file, 'rb') as f:
                    file_hash = hashlib.sha256(f.read()).hexdigest()
                
                # Store expected hash (this would be set during build)
                # For now, just check if file size is reasonable
                file_size = current_file.stat().st_size
                if file_size < 1000:  # Too small, might be tampered
                    return False
                    
        except Exception:
            return False
            
        return True
    
    def anti_dump_protection(self):
        """Basic anti-memory dump protection"""
        try:
            # Allocate and fill some dummy memory
            dummy_data = []
            for _ in range(100):
                dummy_data.append(os.urandom(1024))
            
            # Clear sensitive variables periodically
            if hasattr(self, '_sensitive_data'):
                del self._sensitive_data
                
        except Exception:
            pass
    
    def obfuscate_control_flow(self):
        """Add some control flow obfuscation"""
        # Random delays and dummy operations
        operations = [
            lambda: time.sleep(random.uniform(0.001, 0.01)),
            lambda: [random.randint(1, 100) for _ in range(10)],
            lambda: ''.join(random.choices(string.ascii_letters, k=20)),
            lambda: sum(range(random.randint(1, 50)))
        ]
        
        # Execute random operations
        for _ in range(random.randint(1, 3)):
            random.choice(operations)()
    
    def protection_monitor(self):
        """Main protection monitoring loop"""
        while self.protection_active:
            try:
                # Perform various checks
                if self.check_debugger_processes():
                    self._handle_threat("Debugger detected")
                    
                if self.check_timing_attacks():
                    self._handle_threat("Timing analysis detected")
                    
                if not self.check_file_integrity():
                    self._handle_threat("File integrity compromised")
                
                # Anti-dump protection
                self.anti_dump_protection()
                
                # Control flow obfuscation
                self.obfuscate_control_flow()
                
                # Sleep with random interval
                time.sleep(random.uniform(2, 5))
                
            except Exception:
                # Silently continue on errors
                time.sleep(1)
    
    def _handle_threat(self, threat_type):
        """Handle detected threats"""
        self.debug_detected = True
        
        # Log the threat (could be sent to server in production)
        print(f"Security threat detected: {threat_type}")
        
        # Implement countermeasures
        self._deploy_countermeasures()
    
    def _deploy_countermeasures(self):
        """Deploy countermeasures against threats"""
        # Corrupt memory to make analysis harder
        try:
            # Fill memory with garbage
            garbage = [os.urandom(1024) for _ in range(50)]
            
            # Trigger garbage collection
            import gc
            gc.collect()
            
            # Exit gracefully after a delay
            threading.Timer(2.0, self._emergency_exit).start()
            
        except Exception:
            pass
    
    def _emergency_exit(self):
        """Emergency exit procedure"""
        try:
            # Clear sensitive data
            if hasattr(self, '_strings'):
                del self._strings
                
            # Force exit
            os._exit(1)
            
        except Exception:
            sys.exit(1)
    
    def start_monitoring(self):
        """Start protection monitoring in background thread"""
        if self.monitoring_thread is None or not self.monitoring_thread.is_alive():
            self.monitoring_thread = threading.Thread(
                target=self.protection_monitor,
                daemon=True
            )
            self.monitoring_thread.start()
    
    def stop_monitoring(self):
        """Stop protection monitoring"""
        self.protection_active = False
        if self.monitoring_thread and self.monitoring_thread.is_alive():
            self.monitoring_thread.join(timeout=1)
    
    def is_safe_environment(self):
        """Check if environment is safe for execution"""
        # Quick safety check
        if self.debug_detected:
            return False
            
        if self.check_vm_environment():
            return False
            
        return True
    
    def get_protection_status(self):
        """Get current protection status"""
        return {
            'active': self.protection_active,
            'debug_detected': self.debug_detected,
            'uptime': time.time() - self.start_time,
            'safe_environment': self.is_safe_environment()
        }

# Global protection instance
_protection_instance = None

def get_protection_manager():
    """Get global protection manager instance"""
    global _protection_instance
    if _protection_instance is None:
        _protection_instance = ProtectionManager()
    return _protection_instance

def verify_execution_environment():
    """Verify that execution environment is safe"""
    protection = get_protection_manager()
    return protection.is_safe_environment()
