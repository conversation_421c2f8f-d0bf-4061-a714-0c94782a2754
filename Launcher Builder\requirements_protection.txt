# Requirements for building protected version of Fusion Launcher

# Core dependencies (already in main requirements)
customtkinter>=5.2.0
Pillow>=10.0.0
requests>=2.31.0
psutil>=5.9.0
pypresence>=4.3.0

# Protection and build dependencies
cython>=3.0.0
pyarmor>=8.4.0
pyinstaller>=6.0.0
numpy>=1.24.0

# Optional compression
upx-ucl>=4.0.0  # Note: UPX needs to be installed separately

# Development tools (for testing)
pytest>=7.4.0
pytest-cov>=4.1.0
