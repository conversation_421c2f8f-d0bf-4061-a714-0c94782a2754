"""
Test script to verify client protection features are working
"""

def test_client_cython_modules():
    """Test if client Cython modules can be imported"""
    try:
        import client_core_module
        print("✅ client_core_module imported successfully")
        
        # Test core functionality
        core = client_core_module.create_client_core()
        if core:
            print("✅ ClientCore created successfully")
            
            # Test protected data access
            config = core.get_runtime_config()
            if config and len(config) > 0:
                print("✅ Protected client configuration loaded successfully")
                print(f"   - Sound URLs: {'***PROTECTED***' if config.get('sound_urls') else 'MISSING'}")
                print(f"   - Memory Patterns: {'***PROTECTED***' if config.get('memory_patterns') else 'MISSING'}")
                print(f"   - Static Offsets: {'***PROTECTED***' if config.get('static_offsets') else 'MISSING'}")
            else:
                print("❌ Failed to load protected client configuration")
        else:
            print("❌ Failed to create <PERSON>lient<PERSON>ore")
            
    except ImportError as e:
        print(f"❌ Failed to import client_core_module: {e}")
    except Exception as e:
        print(f"❌ Error testing client_core_module: {e}")

def test_client_protection_module():
    """Test if client protection module is working"""
    try:
        import client_protection
        print("✅ client_protection module imported successfully")
        
        # Test protection manager
        pm = client_protection.get_client_protection_manager()
        if pm:
            print("✅ ClientProtectionManager created successfully")
            
            # Test environment verification
            is_safe = client_protection.verify_client_execution_environment()
            print(f"✅ Client environment verification: {'SAFE' if is_safe else 'UNSAFE'}")
            
            # Get protection status
            status = pm.get_protection_status()
            print(f"✅ Client protection status:")
            print(f"   - Active: {status.get('active', False)}")
            print(f"   - Debug detected: {status.get('debug_detected', False)}")
            print(f"   - Safe environment: {status.get('safe_environment', False)}")
            print(f"   - Minecraft running: {status.get('minecraft_running', False)}")
            print(f"   - Uptime: {status.get('uptime', 0):.2f}s")
        else:
            print("❌ Failed to create ClientProtectionManager")
            
    except ImportError as e:
        print(f"❌ Failed to import client_protection: {e}")
    except Exception as e:
        print(f"❌ Error testing client_protection: {e}")

def test_client_string_obfuscation():
    """Test client string obfuscation functionality"""
    try:
        import client_core_module
        core = client_core_module.create_client_core()
        
        if core:
            # Test that URLs are properly obfuscated in memory
            test_string = "test_client_string_456"
            obfuscated = core._obfuscate_string(test_string)
            deobfuscated = core._deobfuscate_string(obfuscated)
            
            if deobfuscated == test_string:
                print("✅ Client string obfuscation/deobfuscation working correctly")
                print(f"   - Original: {test_string}")
                print(f"   - Obfuscated: {obfuscated[:25]}...")
                print(f"   - Deobfuscated: {deobfuscated}")
            else:
                print("❌ Client string obfuscation failed")
                
            # Test byte obfuscation
            test_bytes = [0x90, 0x90, 0x90, 0x90]
            obfuscated_bytes = core._obfuscate_bytes(test_bytes)
            deobfuscated_bytes = core._deobfuscate_bytes(obfuscated_bytes)
            
            if list(deobfuscated_bytes) == test_bytes:
                print("✅ Client byte obfuscation/deobfuscation working correctly")
                print(f"   - Original bytes: {test_bytes}")
                print(f"   - Obfuscated: {obfuscated_bytes[:20]}...")
                print(f"   - Deobfuscated: {list(deobfuscated_bytes)}")
            else:
                print("❌ Client byte obfuscation failed")
        else:
            print("❌ Cannot test client string obfuscation - core module not available")
            
    except Exception as e:
        print(f"❌ Error testing client string obfuscation: {e}")

def test_client_memory_patterns():
    """Test client memory pattern protection"""
    try:
        import client_core_module
        core = client_core_module.create_client_core()
        
        if core:
            patterns = core.get_memory_patterns()
            if patterns and len(patterns) > 0:
                print("✅ Client memory patterns loaded successfully")
                print(f"   - Total patterns: {len(patterns)}")
                
                # Test some key patterns
                key_patterns = ['air_jump_pattern', 'phase_pattern', 'fly_pattern', 'antikb_pattern1']
                for pattern_name in key_patterns:
                    if pattern_name in patterns:
                        pattern_data = patterns[pattern_name]
                        print(f"   - {pattern_name}: {len(pattern_data)} bytes")
                    else:
                        print(f"   - {pattern_name}: MISSING")
            else:
                print("❌ Failed to load client memory patterns")
        else:
            print("❌ Cannot test client memory patterns - core module not available")
            
    except Exception as e:
        print(f"❌ Error testing client memory patterns: {e}")

def test_client_fallback_mode():
    """Test client fallback mode when protection is not available"""
    print("\n🔄 Testing client fallback mode...")
    
    # This simulates what happens when protection modules are not available
    try:
        # Test basic client functionality without protection
        import FusionClient
        print("✅ FusionClient can be imported")
        
        # The client should work even without protection modules
        print("✅ Client fallback mode should be functional")
        
    except Exception as e:
        print(f"❌ Error in client fallback mode: {e}")

def test_client_integration():
    """Test client integration with protection system"""
    try:
        # Test if FusionClient can use protected modules
        import FusionClient
        
        # Create a client instance (this will test protection integration)
        print("🔄 Testing client protection integration...")
        
        # Note: We can't actually create a FusionClient instance here because
        # it requires Minecraft to be running and GUI setup, but we can test
        # the import and basic class structure
        
        if hasattr(FusionClient, 'FusionClient'):
            print("✅ FusionClient class available")
            
            # Check if protection attributes exist
            if hasattr(FusionClient, 'CLIENT_PROTECTION_LOADED'):
                protection_status = FusionClient.CLIENT_PROTECTION_LOADED
                print(f"✅ Client protection status: {'LOADED' if protection_status else 'FALLBACK'}")
            else:
                print("❌ Client protection status not available")
        else:
            print("❌ FusionClient class not found")
            
    except Exception as e:
        print(f"❌ Error testing client integration: {e}")

def main():
    """Run all client protection tests"""
    print("🔒 Fusion Client Protection System Test")
    print("=" * 60)
    
    print("\n📦 Testing Client Cython Modules:")
    test_client_cython_modules()
    
    print("\n🛡️ Testing Client Protection Features:")
    test_client_protection_module()
    
    print("\n🔐 Testing Client String Obfuscation:")
    test_client_string_obfuscation()
    
    print("\n🧠 Testing Client Memory Patterns:")
    test_client_memory_patterns()
    
    test_client_fallback_mode()
    
    print("\n🔗 Testing Client Integration:")
    test_client_integration()
    
    print("\n" + "=" * 60)
    print("🏁 Client protection system test completed!")
    print("\nIf all tests show ✅, your client protection system is working correctly.")
    print("If any tests show ❌, check the error messages above.")

if __name__ == "__main__":
    main()
