"""
Core module for Fusion Launcher
Contains sensitive logic and obfuscated data
This module should be compiled with Cython for additional protection
"""

import base64
import hashlib
import time
import random
from protection import get_protection_manager

class CoreLauncher:
    """Core launcher functionality with obfuscated data"""
    
    def __init__(self):
        self.protection = get_protection_manager()
        self._init_obfuscated_data()
        
        # Verify environment before proceeding
        if not self.protection.is_safe_environment():
            raise RuntimeError("Unsafe execution environment detected")
    
    def _init_obfuscated_data(self):
        """Initialize obfuscated sensitive data"""
        # XOR key for string obfuscation
        self._key = 0x5A
        
        # Obfuscated URLs and sensitive strings
        self._obfuscated_data = {
            # Version URL (obfuscated)
            'version_url': self._obfuscate_string(
                "https://www.dropbox.com/scl/fi/sfwl8gneljrgc1qrlimnj/version.txt?rlkey=mo5f9u2uvl28q8qei7mpru907&st=ia2r2d8c&dl=1"
            ),
            
            # EXE URL (obfuscated)
            'exe_url': self._obfuscate_string(
                "https://dl.dropboxusercontent.com/scl/fi/176p5t6fgsfm8m87g2elp/FusionClient.exe?rlkey=oxqavcolm03h1nr4tl8z3d40s&st=8oxrddsp"
            ),
            
            # Discord invite (obfuscated)
            'discord_invite': self._obfuscate_string(
                "https://discord.gg/5pRZnHjvJ4"
            ),
            
            # Discord client ID (obfuscated)
            'discord_client_id': self._obfuscate_string(
                "1377037066818293900"
            ),
            
            # Process name (obfuscated)
            'minecraft_process': self._obfuscate_string(
                "Minecraft.Windows.exe"
            )
        }
        
        # Additional protection layer - checksum verification
        self._verify_data_integrity()
    
    def _obfuscate_string(self, text):
        """Obfuscate string using XOR and base64"""
        # First XOR
        xor_result = ''.join(chr(ord(c) ^ self._key) for c in text)
        
        # Then base64 encode
        encoded = base64.b64encode(xor_result.encode('utf-8')).decode('utf-8')
        
        # Add some random padding
        padding = ''.join(random.choices('ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789', k=4))
        
        return padding + encoded + padding
    
    def _deobfuscate_string(self, obfuscated_text):
        """Deobfuscate string"""
        try:
            # Remove padding (first and last 4 characters)
            encoded = obfuscated_text[4:-4]
            
            # Base64 decode
            decoded = base64.b64decode(encoded.encode('utf-8')).decode('utf-8')
            
            # XOR decode
            result = ''.join(chr(ord(c) ^ self._key) for c in decoded)
            
            return result
        except Exception:
            # Return empty string on decode failure
            return ""
    
    def _verify_data_integrity(self):
        """Verify integrity of obfuscated data"""
        # Calculate checksum of obfuscated data
        data_str = str(self._obfuscated_data)
        checksum = hashlib.md5(data_str.encode()).hexdigest()
        
        # Store checksum for later verification
        self._data_checksum = checksum
    
    def _anti_tamper_check(self):
        """Check if data has been tampered with"""
        current_checksum = hashlib.md5(str(self._obfuscated_data).encode()).hexdigest()
        return current_checksum == self._data_checksum
    
    def get_version_url(self):
        """Get deobfuscated version URL"""
        if not self._anti_tamper_check():
            raise RuntimeError("Data integrity compromised")
            
        return self._deobfuscate_string(self._obfuscated_data['version_url'])
    
    def get_exe_url(self):
        """Get deobfuscated exe URL"""
        if not self._anti_tamper_check():
            raise RuntimeError("Data integrity compromised")
            
        return self._deobfuscate_string(self._obfuscated_data['exe_url'])
    
    def get_discord_invite(self):
        """Get deobfuscated Discord invite"""
        if not self._anti_tamper_check():
            raise RuntimeError("Data integrity compromised")
            
        return self._deobfuscate_string(self._obfuscated_data['discord_invite'])
    
    def get_discord_client_id(self):
        """Get deobfuscated Discord client ID"""
        if not self._anti_tamper_check():
            raise RuntimeError("Data integrity compromised")
            
        return self._deobfuscate_string(self._obfuscated_data['discord_client_id'])
    
    def get_minecraft_process_name(self):
        """Get deobfuscated Minecraft process name"""
        if not self._anti_tamper_check():
            raise RuntimeError("Data integrity compromised")
            
        return self._deobfuscate_string(self._obfuscated_data['minecraft_process'])
    
    def get_mcpack_config(self):
        """Get obfuscated mcpack configuration"""
        if not self._anti_tamper_check():
            raise RuntimeError("Data integrity compromised")
        
        # Obfuscated mcpack URLs
        pack_configs = [
            {
                "name_url": self._deobfuscate_string(self._obfuscate_string(
                    "https://www.dropbox.com/scl/fi/63miijl8hiqdnywg1uwy2/packname1.txt?rlkey=hi7q4gf806uzmzeonfc80ppp0&dl=1"
                )),
                "icon_url": self._deobfuscate_string(self._obfuscate_string(
                    "https://www.dropbox.com/scl/fi/81evf2v9ni6xyq1i63y3o/pack_icon1.png?rlkey=97fwifp41s9sxm84fferlaicy&dl=1"
                )),
                "pack_url": self._deobfuscate_string(self._obfuscate_string(
                    "https://www.dropbox.com/scl/fi/ikts12jh8xygv699z7u0u/PvPSounds.mcpack?rlkey=bgg86ezzufmalq0lh8vpnolb5&dl=1"
                )),
                "fallback_name": "PvPSounds"
            },
            {
                "name_url": self._deobfuscate_string(self._obfuscate_string(
                    "https://www.dropbox.com/scl/fi/h5rqwkgkzv7giujlg1gt6/packname2.txt?rlkey=x3qwd03bp0vdonmac0tkhrepr&dl=1"
                )),
                "icon_url": self._deobfuscate_string(self._obfuscate_string(
                    "https://www.dropbox.com/scl/fi/m2cp8vowsph8w5t2zzgyg/pack_icon2.png?rlkey=t91ns2r31yalk91opyzzsdieb&dl=1"
                )),
                "pack_url": self._deobfuscate_string(self._obfuscate_string(
                    "https://www.dropbox.com/scl/fi/dyjl7mkkr05tuq582v7r7/Nebula-32x.mcpack?rlkey=4lvq7mixw17uuat72otgu62fy&dl=1"
                )),
                "fallback_name": "Nebula-32x"
            }
        ]
        
        return pack_configs
    
    def validate_execution_context(self):
        """Validate that we're running in the correct context"""
        # Check protection status
        if not self.protection.is_safe_environment():
            return False
        
        # Additional runtime checks
        if self.protection.debug_detected:
            return False
            
        # Check if data integrity is maintained
        if not self._anti_tamper_check():
            return False
            
        return True
    
    def perform_runtime_checks(self):
        """Perform runtime security checks"""
        # Anti-debugging check
        start_time = time.perf_counter()
        
        # Dummy operations to detect debugging
        dummy_ops = []
        for i in range(1000):
            dummy_ops.append(i * 2 + 1)
        
        end_time = time.perf_counter()
        
        # If execution took too long, might be under debugging
        if end_time - start_time > 0.1:
            raise RuntimeError("Execution anomaly detected")
        
        # Memory protection
        self._protect_memory()
    
    def _protect_memory(self):
        """Basic memory protection"""
        # Allocate dummy memory to confuse memory scanners
        dummy_data = []
        for _ in range(50):
            dummy_data.append(b'\x00' * 1024)
        
        # Clear dummy data
        del dummy_data
    
    def get_runtime_config(self):
        """Get runtime configuration with all deobfuscated data"""
        if not self.validate_execution_context():
            raise RuntimeError("Invalid execution context")
        
        self.perform_runtime_checks()
        
        return {
            'version_url': self.get_version_url(),
            'exe_url': self.get_exe_url(),
            'discord_invite': self.get_discord_invite(),
            'discord_client_id': self.get_discord_client_id(),
            'minecraft_process_name': self.get_minecraft_process_name(),
            'mcpack_config': self.get_mcpack_config()
        }

# Factory function for creating core launcher instance
def create_core_launcher():
    """Create and return a CoreLauncher instance"""
    try:
        return CoreLauncher()
    except Exception as e:
        # Log error and return None
        print(f"Failed to create core launcher: {e}")
        return None

# Verification function
def verify_core_integrity():
    """Verify core module integrity"""
    try:
        core = create_core_launcher()
        if core is None:
            return False
        
        # Test basic functionality
        config = core.get_runtime_config()
        return len(config) > 0
        
    except Exception:
        return False
