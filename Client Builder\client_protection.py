"""
Protection module for Fusion Client
Implements anti-debugging and runtime protection measures specifically for the client
"""

import os
import sys
import time
import psutil
import threading
import hashlib
import random
import string
from pathlib import Path

class ClientProtectionManager:
    """Manages runtime protection and anti-debugging measures for the client"""

    def __init__(self):
        self.protection_active = True
        self.debug_detected = False
        self.monitoring_thread = None
        self.start_time = time.time()

        # Obfuscated strings
        self._strings = self._init_strings()

        # Start protection monitoring
        self.start_monitoring()

    def _init_strings(self):
        """Initialize obfuscated strings"""
        # Simple XOR obfuscation for sensitive strings
        key = 0x55
        strings = {}

        # Obfuscated analysis tool names
        analysis_tools = [
            "cheatengine.exe",
            "processhacker.exe",
            "x64dbg.exe",
            "ollydbg.exe",
            "ida.exe",
            "ida64.exe",
            "windbg.exe",
            "procmon.exe",
            "wireshark.exe",
            "fiddler.exe",
            "dnspy.exe",
            "reflexil.exe",
            "de4dot.exe",
            "ilspy.exe",
            "dotpeek.exe",
            "reshacker.exe",
            "pestudio.exe",
            "pe-bear.exe",
            "hxd.exe",
            "010editor.exe"
        ]

        strings['analysis_tools'] = [self._xor_string(name, key) for name in analysis_tools]

        # Obfuscated game process name
        strings['minecraft_process'] = self._xor_string("Minecraft.Windows.exe", key)

        return strings

    def _xor_string(self, text, key):
        """Simple XOR string obfuscation"""
        return ''.join(chr(ord(c) ^ key) for c in text)

    def _decode_string(self, encoded, key=0x55):
        """Decode XOR obfuscated string"""
        return self._xor_string(encoded, key)

    def check_analysis_tools(self):
        """Check for known analysis and reverse engineering tools"""
        try:
            for proc in psutil.process_iter(['name']):
                proc_name = proc.info['name'].lower()

                # Check against obfuscated tool names
                for encoded_name in self._strings['analysis_tools']:
                    tool_name = self._decode_string(encoded_name).lower()
                    if tool_name in proc_name:
                        self.debug_detected = True
                        return True

                # Additional checks for memory scanners
                suspicious_keywords = [
                    'memory', 'scanner', 'trainer', 'hack', 'cheat',
                    'inject', 'dll', 'mod', 'bypass', 'crack'
                ]

                for keyword in suspicious_keywords:
                    if keyword in proc_name:
                        self.debug_detected = True
                        return True

        except Exception:
            pass

        return False

    def check_minecraft_process(self):
        """Check if Minecraft process is running"""
        try:
            minecraft_process = self._decode_string(self._strings['minecraft_process'])

            for proc in psutil.process_iter(['name']):
                if proc.info['name'] == minecraft_process:
                    return True

        except Exception:
            pass

        return False

    def check_vm_environment(self):
        """Check if running in a virtual machine"""
        try:
            # Check for VM-specific processes
            vm_processes = [
                'vmtoolsd.exe', 'vmwaretray.exe', 'vmwareuser.exe',
                'vboxservice.exe', 'vboxtray.exe', 'xenservice.exe',
                'qemu-ga.exe', 'prl_tools.exe'
            ]

            for proc in psutil.process_iter(['name']):
                if proc.info['name'].lower() in vm_processes:
                    return True

            # Check for VM-specific registry keys (Windows)
            if os.name == 'nt':
                import winreg
                vm_keys = [
                    r"SOFTWARE\VMware, Inc.\VMware Tools",
                    r"SOFTWARE\Oracle\VirtualBox Guest Additions",
                    r"SYSTEM\ControlSet001\Services\VBoxGuest"
                ]

                for key_path in vm_keys:
                    try:
                        winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, key_path)
                        return True
                    except FileNotFoundError:
                        continue

        except Exception:
            pass

        return False

    def check_timing_attacks(self):
        """Check for timing-based analysis"""
        # Measure execution time for simple operations
        start = time.perf_counter()

        # Perform some dummy operations
        dummy = 0
        for i in range(2000):
            dummy += i * 5 + 3

        end = time.perf_counter()
        execution_time = end - start

        # Make timing check much more lenient to prevent false positives
        if execution_time > 1.0:  # 1 second threshold (was 150ms)
            print(f"Protection: Slow execution detected ({execution_time:.3f}s) - possible debugging")
            return True

        return False

    def check_file_integrity(self):
        """Check if the current file has been modified"""
        try:
            current_file = Path(__file__)
            if current_file.exists():
                # Calculate file hash
                with open(current_file, 'rb') as f:
                    file_hash = hashlib.sha256(f.read()).hexdigest()

                # Store expected hash (this would be set during build)
                # For now, just check if file size is reasonable
                file_size = current_file.stat().st_size
                if file_size < 2000:  # Too small, might be tampered
                    return False

        except Exception:
            return False

        return True

    def anti_dump_protection(self):
        """Basic anti-memory dump protection"""
        try:
            # Allocate and fill some dummy memory with patterns
            dummy_data = []
            for _ in range(150):
                # Create patterns that look like real data
                pattern = os.urandom(2048)
                dummy_data.append(pattern)

            # Clear sensitive variables periodically
            if hasattr(self, '_sensitive_data'):
                del self._sensitive_data

        except Exception:
            pass

    def obfuscate_control_flow(self):
        """Add some control flow obfuscation"""
        # Random delays and dummy operations
        operations = [
            lambda: time.sleep(random.uniform(0.001, 0.02)),
            lambda: [random.randint(1, 200) for _ in range(20)],
            lambda: ''.join(random.choices(string.ascii_letters + string.digits, k=50)),
            lambda: sum(range(random.randint(1, 100))),
            lambda: hashlib.md5(os.urandom(64)).hexdigest()
        ]

        # Execute random operations
        for _ in range(random.randint(2, 5)):
            random.choice(operations)()

    def protection_monitor(self):
        """Main protection monitoring loop"""
        while self.protection_active:
            try:
                # Perform various checks
                if self.check_analysis_tools():
                    self._handle_threat("Analysis tool detected")

                if self.check_timing_attacks():
                    self._handle_threat("Timing analysis detected")

                if not self.check_file_integrity():
                    self._handle_threat("File integrity compromised")

                # Anti-dump protection
                self.anti_dump_protection()

                # Control flow obfuscation
                self.obfuscate_control_flow()

                # Sleep with random interval
                time.sleep(random.uniform(3, 7))

            except Exception:
                # Silently continue on errors
                time.sleep(2)

    def _handle_threat(self, threat_type):
        """Handle detected threats"""
        self.debug_detected = True

        # Log the threat (could be sent to server in production)
        print(f"Client security threat detected: {threat_type}")

        # For now, just log and monitor - don't deploy aggressive countermeasures
        # This prevents the client from auto-closing during normal usage
        print("Protection: Monitoring threat, but allowing execution to continue")

        # Only deploy countermeasures for serious threats
        if "Analysis tool" in threat_type and "cheatengine" in threat_type.lower():
            print("Protection: Serious analysis tool detected - deploying countermeasures")
            self._deploy_countermeasures()
        else:
            print("Protection: Minor threat detected - continuing monitoring")

    def _deploy_countermeasures(self):
        """Deploy countermeasures against threats"""
        # Corrupt memory to make analysis harder
        try:
            # Fill memory with garbage patterns
            garbage = []
            for _ in range(100):
                garbage.append(os.urandom(4096))

            # Trigger garbage collection
            import gc
            gc.collect()

            # Exit gracefully after a delay
            threading.Timer(3.0, self._emergency_exit).start()

        except Exception:
            pass

    def _emergency_exit(self):
        """Emergency exit procedure"""
        try:
            # Clear sensitive data
            if hasattr(self, '_strings'):
                del self._strings

            # Force exit
            os._exit(1)

        except Exception:
            sys.exit(1)

    def start_monitoring(self):
        """Start protection monitoring in background thread"""
        if self.monitoring_thread is None or not self.monitoring_thread.is_alive():
            self.monitoring_thread = threading.Thread(
                target=self.protection_monitor,
                daemon=True
            )
            self.monitoring_thread.start()

    def stop_monitoring(self):
        """Stop protection monitoring"""
        self.protection_active = False
        if self.monitoring_thread and self.monitoring_thread.is_alive():
            self.monitoring_thread.join(timeout=2)

    def is_safe_environment(self):
        """Check if environment is safe for execution"""
        # Quick safety check - but don't fail immediately
        if self.debug_detected:
            print("Protection: Debug tools detected, monitoring...")
            # Don't return False immediately, just monitor

        # VM detection - warn but allow execution
        if self.check_vm_environment():
            print("Protection: VM environment detected, but allowing execution")

        # Check if Minecraft is running (not required for startup)
        if not self.check_minecraft_process():
            print("Protection: Minecraft not running - this is normal for client startup")

        # Always return True to prevent auto-close during normal usage
        # Only exit if serious threats are detected in monitoring loop
        return True

    def get_protection_status(self):
        """Get current protection status"""
        return {
            'active': self.protection_active,
            'debug_detected': self.debug_detected,
            'uptime': time.time() - self.start_time,
            'safe_environment': self.is_safe_environment(),
            'minecraft_running': self.check_minecraft_process()
        }

# Global protection instance
_client_protection_instance = None

def get_client_protection_manager():
    """Get global client protection manager instance"""
    global _client_protection_instance
    if _client_protection_instance is None:
        _client_protection_instance = ClientProtectionManager()
    return _client_protection_instance

def verify_client_execution_environment():
    """Verify that client execution environment is safe"""
    protection = get_client_protection_manager()
    return protection.is_safe_environment()
