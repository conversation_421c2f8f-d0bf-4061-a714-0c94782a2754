# -*- mode: python ; coding: utf-8 -*-


a = Analysis(
    ['FusionLauncher.py'],
    pathex=[],
    binaries=[],
    datas=[('core_module.cp313-win_amd64.pyd', '.'), ('protection.cp313-win_amd64.pyd', '.')],
    hiddenimports=['core_module', 'protection', 'customtkinter', 'PIL', 'requests', 'psutil', 'pypresence', 'numpy'],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
    optimize=0,
)
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='FusionLauncher',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
