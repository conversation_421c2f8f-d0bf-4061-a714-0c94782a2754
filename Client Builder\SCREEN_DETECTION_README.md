# Screen Detection Feature

## Übersicht

Das Screen Detection System erkennt automatisch, wenn das Minecraft-Inventar oder eine Truhe geöffnet ist und pausiert AutoClicker und Double Click, um versehentliche Klicks in Menü<PERSON> zu verhindern.

## Funktionen

- **Inventar-Erkennung**: Erkennt das Inventar durch den "Handwerk" Text
- **Truhen-Erkennung**: Erkennt geöffnete Truhen und andere UI-Elemente
- **Automatische Pause & Resume**: Pausiert AutoClicker und Double Click automatisch und nimmt sie wieder auf, wenn Menüs geschlossen werden
- **Nur Minecraft-Fenster**: Scannt nur das aktive Minecraft-Fenster
- **Fallback-System**: Funktioniert auch ohne OCR-Software
- **Kontinuierliche Überwachung**: Prüft alle 500ms und reagiert sofort auf Änderungen

## Installation

### Basis-Installation (ohne OCR)
Das System funktioniert bereits mit der Standard-Installation und verwendet eine einfache Pixel-basierte Erkennung.

### Erweiterte Installation (mit OCR)
Für bessere Text-Erkennung kann optional Tesseract OCR installiert werden:

1. **Tesseract OCR herunterladen**:
   - Download von: https://github.com/UB-Mannheim/tesseract/wiki
   - Installiere Tesseract für Windows

2. **Python-Paket installieren**:
   ```bash
   pip install pytesseract
   ```

3. **Tesseract-Pfad konfigurieren** (falls nötig):
   ```python
   # In FusionClient.py, falls Tesseract nicht im PATH ist:
   pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'
   ```

## Verwendung

1. **Aktivierung**: Gehe zu Settings → Detection Tab
2. **Screen Detection einschalten**: Aktiviere den "Enable Screen Detection" Switch
3. **Status überwachen**: Der Status wird in Echtzeit angezeigt
4. **Automatisches Verhalten**:
   - AutoClicker/DoubleClick pausieren automatisch bei geöffnetem Inventar/Truhe
   - Sie nehmen automatisch wieder auf, sobald das Menü geschlossen wird
   - Keine manuelle Intervention erforderlich

## Erkennungslogik

### Inventar-Erkennung
- Sucht nach "Handwerk" oder "Crafting" Text
- Verwendet OCR wenn verfügbar, sonst Fallback-Methode

### Truhen-Erkennung
- Analysiert Pixel-Muster für dunkle UI-Hintergründe
- Erkennt typische Minecraft-UI-Elemente

### Fallback-System
Ohne OCR verwendet das System eine einfache Heuristik:
- Sampelt Pixel in einem Raster-Muster
- Zählt dunkle Pixel (UI-Hintergründe)
- Erkennt UI wenn >30% der Pixel dunkel sind

## Konfiguration

### Einstellbare Parameter
```python
self.screen_check_interval = 0.5  # Prüfung alle 500ms
self.screen_detection_enabled = False  # Standard: deaktiviert
```

### Performance-Optimierung
- Prüfung nur alle 500ms (konfigurierbar)
- Nur bei aktivem Minecraft-Fenster
- Minimaler CPU-Verbrauch

## Fehlerbehebung

### Screen Detection funktioniert nicht
1. Prüfe ob Minecraft-Fenster erkannt wird
2. Teste mit verschiedenen UI-Elementen
3. Prüfe Console-Output für Fehlermeldungen

### Falsche Erkennungen
1. Justiere `screen_check_interval` für häufigere Prüfungen
2. Bei OCR: Prüfe Tesseract-Installation
3. Fallback-Methode: Justiere Schwellenwerte

### Performance-Probleme
1. Erhöhe `screen_check_interval` (z.B. auf 1.0 Sekunde)
2. Deaktiviere OCR und nutze nur Fallback-Methode

## Technische Details

### Architektur
- Separater Thread für Screen Detection
- Asynchrone Screenshot-Aufnahme
- Thread-sichere UI-Updates

### Abhängigkeiten
- **PIL (Pillow)**: Screenshot-Aufnahme
- **win32gui**: Fenster-Erkennung
- **pytesseract** (optional): OCR-Text-Erkennung

### Sicherheit
- Nur Minecraft-Fenster wird gescannt
- Keine Screenshots gespeichert
- Lokale Verarbeitung (keine Netzwerk-Übertragung)
