"""
Setup script for compiling Python modules to Cython extensions
This provides additional protection against decompilation
"""

from setuptools import setup, Extension
from Cython.Build import cythonize
import numpy

# Define extensions to compile
extensions = [
    Extension(
        "core_module",
        ["core_module.py"],
        include_dirs=[numpy.get_include()],
    ),
    Extension(
        "protection",
        ["protection.py"],
        include_dirs=[numpy.get_include()],
    )
]

# Cython compiler directives for optimization and obfuscation
compiler_directives = {
    'language_level': 3,
    'boundscheck': False,
    'wraparound': False,
    'initializedcheck': False,
    'cdivision': True,
    'embedsignature': False,  # Don't embed function signatures
    'emit_code_comments': False,  # Don't emit code comments
    'profile': False,  # Disable profiling
}

setup(
    name="FusionLauncherCore",
    ext_modules=cythonize(
        extensions,
        compiler_directives=compiler_directives,
        build_dir="build",
        language_level=3
    ),
    zip_safe=False,
)
