"""
Core module for Fusion Client
Contains sensitive logic and obfuscated data
This module should be compiled with Cython for additional protection
"""

import base64
import hashlib
import time
import random
from client_protection import get_client_protection_manager

class ClientCore:
    """Core client functionality with obfuscated data"""

    def __init__(self):
        self.protection = get_client_protection_manager()
        self._init_obfuscated_data()

        # Verify environment before proceeding - but don't fail hard
        if not self.protection.is_safe_environment():
            print("ClientCore: Environment check failed, but continuing execution")
            # Don't raise exception to prevent auto-close

    def _init_obfuscated_data(self):
        """Initialize obfuscated sensitive data"""
        # XOR key for string obfuscation
        self._key = 0x7B

        # Obfuscated URLs and sensitive strings
        self._obfuscated_data = {
            # Sound URLs (obfuscated)
            'enable_sound_url': self._obfuscate_string(
                "https://www.dropbox.com/scl/fi/u4f0kjepvnj9jtsm91zp3/ENABLE.wav?rlkey=19k0sx3m2yfgq8215kadhu3u8&st=re585s91&dl=1"
            ),

            'disable_sound_url': self._obfuscate_string(
                "https://www.dropbox.com/scl/fi/ool3u4yhqbl2eztrac1c8/DISABLE.wav?rlkey=hrf3kvgfkk05itmjgnvknqa1w&st=qevknf9l&dl=1"
            ),

            # Memory patterns (obfuscated for anti-analysis)
            'air_jump_pattern': self._obfuscate_bytes([0x0F, 0x2E, 0x77, 0x34, 0x75, 0x0D]),
            'air_jump_patch': self._obfuscate_bytes([0x90, 0x90, 0x90, 0x90, 0x90, 0x90]),

            'fast_ladder_pattern': self._obfuscate_bytes([0xC7, 0x47, 0x1C, 0xCD, 0xCC, 0x4C, 0x3E]),

            'high_jump_pattern': self._obfuscate_bytes([0x89, 0x01, 0x41, 0xF6, 0x04, 0x24, 0x08]),

            'phase_pattern': self._obfuscate_bytes([0xF2, 0x0F, 0x11, 0x4A, 0x10, 0x48, 0x83, 0x43, 0x08, 0x18, 0x48, 0x8B, 0x4C, 0x24, 0x58, 0x48, 0x33, 0xCC, 0xE8, 0x97]),
            'phase_patch': self._obfuscate_bytes([0x90, 0x90, 0x90, 0x90, 0x90, 0x48, 0x83, 0x43, 0x08, 0x18, 0x48, 0x8B, 0x4C, 0x24, 0x58, 0x48, 0x33, 0xCC, 0xE8, 0x97]),

            'no_web_pattern': self._obfuscate_bytes([0xF3, 0x0F, 0x10, 0x2F, 0xF3, 0x0F, 0x10, 0x0D, 0x77, 0x57, 0xF9, 0x02]),
            'no_web_patch': self._obfuscate_bytes([0x90, 0x90, 0x90, 0x90]),

            'fly_pattern': self._obfuscate_bytes([0x40, 0x38, 0x71, 0x04, 0x75, 0x64]),
            'fly_patch': self._obfuscate_bytes([0xC6, 0x41, 0x04, 0x01, 0x90, 0x90]),

            'antikb_pattern1': self._obfuscate_bytes([0xF2, 0x0F, 0x11, 0x40, 0x18, 0x44]),
            'antikb_patch1': self._obfuscate_bytes([0x90, 0x90, 0x90, 0x90, 0x90, 0x44]),

            'antikb_pattern2': self._obfuscate_bytes([0x44, 0x89, 0x40, 0x20, 0x48, 0x83, 0xC4, 0x28]),
            'antikb_patch2': self._obfuscate_bytes([0x90, 0x90, 0x90, 0x90, 0x48, 0x83, 0xC4, 0x28]),

            'scaffold_pattern1': self._obfuscate_bytes([0x74, 0xE2, 0x49, 0x8B, 0x80, 0x18, 0x02, 0x00, 0x00]),
            'scaffold_patch1': self._obfuscate_bytes([0x90, 0x90, 0x49, 0x8B, 0x80, 0x18, 0x02, 0x00, 0x00]),

            'scaffold_pattern2': self._obfuscate_bytes([0x74, 0x19, 0x48, 0x8B, 0x03, 0x48, 0x8D]),
            'scaffold_patch2': self._obfuscate_bytes([0x90, 0x90, 0x48, 0x8B, 0x03, 0x48, 0x8D]),

            'rapid_hit_pattern': self._obfuscate_bytes([0x45, 0x38, 0x30, 0x0F, 0x84, 0xE9, 0x00, 0x00, 0x00]),
            'rapid_hit_patch': self._obfuscate_bytes([0x90, 0x90, 0x90, 0x0F, 0x84, 0xE9, 0x00, 0x00, 0x00]),

            'zoom_pattern': self._obfuscate_bytes([0xF3, 0x0F, 0x10, 0x48, 0x18, 0x48]),
            'zoom_patch': self._obfuscate_bytes([0x90, 0x90, 0x90, 0x90, 0x90, 0x48]),

            'see_entities_pattern': self._obfuscate_bytes([0x44, 0x8B, 0x4A, 0x04, 0x44, 0x8B, 0x42, 0x08, 0x48]),
            'see_entities_patch': self._obfuscate_bytes([0x90, 0x90, 0x90, 0x90, 0x44, 0x8B, 0x42, 0x08, 0x48]),

            'no_hurtcam_pattern': self._obfuscate_bytes([0xC7, 0x00, 0x0A, 0x00, 0x00, 0x00, 0x89]),
            'no_hurtcam_patch': self._obfuscate_bytes([0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x89]),

            'fast_place_pattern': self._obfuscate_bytes([0x0F, 0x2E, 0x47, 0x34, 0x75, 0x0D]),
            'fast_place_patch': self._obfuscate_bytes([0x90, 0x90, 0x90, 0x90, 0x90, 0x90]),

            'auto_sprint_pattern': self._obfuscate_bytes([0x41, 0x88, 0x41, 0x08, 0x0F, 0xB6, 0x42, 0x31]),
            'auto_sprint_patch': self._obfuscate_bytes([0x41, 0xC6, 0x41, 0x08, 0x01, 0x0F, 0xB6, 0x42]),

            # Static offsets (obfuscated)
            'reach_offset': self._obfuscate_value(0x7AF3120),
        }

        # Additional protection layer - checksum verification
        self._verify_data_integrity()

    def _obfuscate_string(self, text):
        """Obfuscate string using XOR and base64"""
        # First XOR
        xor_result = ''.join(chr(ord(c) ^ self._key) for c in text)

        # Then base64 encode
        encoded = base64.b64encode(xor_result.encode('utf-8')).decode('utf-8')

        # Add some random padding
        padding = ''.join(random.choices('ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789', k=6))

        return padding + encoded + padding

    def _obfuscate_bytes(self, byte_list):
        """Obfuscate byte patterns"""
        # XOR each byte with key
        obfuscated = [b ^ self._key for b in byte_list]

        # Encode as base64
        encoded = base64.b64encode(bytes(obfuscated)).decode('utf-8')

        return encoded

    def _obfuscate_value(self, value):
        """Obfuscate integer values"""
        return value ^ 0xDEADBEEF

    def _deobfuscate_string(self, obfuscated_text):
        """Deobfuscate string"""
        try:
            # Remove padding (first and last 6 characters)
            encoded = obfuscated_text[6:-6]

            # Base64 decode
            decoded = base64.b64decode(encoded.encode('utf-8')).decode('utf-8')

            # XOR decode
            result = ''.join(chr(ord(c) ^ self._key) for c in decoded)

            return result
        except Exception:
            return ""

    def _deobfuscate_bytes(self, obfuscated_data):
        """Deobfuscate byte patterns"""
        try:
            # Base64 decode
            decoded = base64.b64decode(obfuscated_data.encode('utf-8'))

            # XOR decode each byte
            result = bytes([b ^ self._key for b in decoded])

            return result
        except Exception:
            return bytes()

    def _deobfuscate_value(self, obfuscated_value):
        """Deobfuscate integer values"""
        return obfuscated_value ^ 0xDEADBEEF

    def _verify_data_integrity(self):
        """Verify integrity of obfuscated data"""
        # Calculate checksum of obfuscated data
        data_str = str(self._obfuscated_data)
        checksum = hashlib.md5(data_str.encode()).hexdigest()

        # Store checksum for later verification
        self._data_checksum = checksum

    def _anti_tamper_check(self):
        """Check if data has been tampered with"""
        current_checksum = hashlib.md5(str(self._obfuscated_data).encode()).hexdigest()
        return current_checksum == self._data_checksum

    def get_sound_urls(self):
        """Get deobfuscated sound URLs"""
        if not self._anti_tamper_check():
            raise RuntimeError("Data integrity compromised")

        return {
            'enable_sound_url': self._deobfuscate_string(self._obfuscated_data['enable_sound_url']),
            'disable_sound_url': self._deobfuscate_string(self._obfuscated_data['disable_sound_url'])
        }

    def get_memory_patterns(self):
        """Get deobfuscated memory patterns"""
        if not self._anti_tamper_check():
            raise RuntimeError("Data integrity compromised")

        patterns = {}

        # Deobfuscate all patterns
        pattern_keys = [
            'air_jump_pattern', 'air_jump_patch',
            'fast_ladder_pattern',
            'high_jump_pattern',
            'phase_pattern', 'phase_patch',
            'no_web_pattern', 'no_web_patch',
            'fly_pattern', 'fly_patch',
            'antikb_pattern1', 'antikb_patch1',
            'antikb_pattern2', 'antikb_patch2',
            'scaffold_pattern1', 'scaffold_patch1',
            'scaffold_pattern2', 'scaffold_patch2',
            'rapid_hit_pattern', 'rapid_hit_patch',
            'zoom_pattern', 'zoom_patch',
            'see_entities_pattern', 'see_entities_patch',
            'no_hurtcam_pattern', 'no_hurtcam_patch',
            'fast_place_pattern', 'fast_place_patch',
            'auto_sprint_pattern', 'auto_sprint_patch'
        ]

        for key in pattern_keys:
            if key in self._obfuscated_data:
                patterns[key] = self._deobfuscate_bytes(self._obfuscated_data[key])

        return patterns

    def get_static_offsets(self):
        """Get deobfuscated static offsets"""
        if not self._anti_tamper_check():
            raise RuntimeError("Data integrity compromised")

        return {
            'reach_offset': self._deobfuscate_value(self._obfuscated_data['reach_offset'])
        }

    def validate_execution_context(self):
        """Validate that we're running in the correct context"""
        # Check protection status
        if not self.protection.is_safe_environment():
            return False

        # Additional runtime checks
        if self.protection.debug_detected:
            return False

        # Check if data integrity is maintained
        if not self._anti_tamper_check():
            return False

        return True

    def perform_runtime_checks(self):
        """Perform runtime security checks"""
        # Anti-debugging check - but don't fail hard
        start_time = time.perf_counter()

        # Dummy operations to detect debugging
        dummy_ops = []
        for i in range(1000):
            dummy_ops.append(i * 3 + 7)

        end_time = time.perf_counter()

        # If execution took too long, might be under debugging - but don't fail
        if end_time - start_time > 0.5:  # Much more lenient (was 0.1)
            print(f"ClientCore: Slow execution detected ({end_time - start_time:.3f}s) - monitoring")
            # Don't raise exception to prevent auto-close

        # Memory protection
        self._protect_memory()

    def _protect_memory(self):
        """Basic memory protection"""
        # Allocate dummy memory to confuse memory scanners
        dummy_data = []
        for _ in range(100):
            dummy_data.append(b'\xFF' * 2048)

        # Clear dummy data
        del dummy_data

    def get_runtime_config(self):
        """Get runtime configuration with all deobfuscated data"""
        # Validate execution context - but don't fail hard
        if not self.validate_execution_context():
            print("ClientCore: Execution context validation failed, but continuing")
            # Don't raise exception to prevent auto-close

        # Perform runtime checks - but don't fail hard
        try:
            self.perform_runtime_checks()
        except Exception as e:
            print(f"ClientCore: Runtime checks failed: {e}, but continuing")

        return {
            'sound_urls': self.get_sound_urls(),
            'memory_patterns': self.get_memory_patterns(),
            'static_offsets': self.get_static_offsets()
        }

# Factory function for creating client core instance
def create_client_core():
    """Create and return a ClientCore instance"""
    try:
        return ClientCore()
    except Exception as e:
        # Log error and return None
        print(f"Failed to create client core: {e}")
        return None

# Verification function
def verify_client_core_integrity():
    """Verify client core module integrity"""
    try:
        core = create_client_core()
        if core is None:
            return False

        # Test basic functionality
        config = core.get_runtime_config()
        return len(config) > 0

    except Exception:
        return False
